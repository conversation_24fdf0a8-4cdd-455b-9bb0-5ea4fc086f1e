import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query, UseFilters, Logger} from '@nestjs/common';
import {TenantService} from './tenant.service';
import {CreateTenantDto} from './dto/create-tenant.dto';
import {UpdateTenantDto} from './dto/update-tenant.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {Tenant} from "./entities/tenant.entity";
import { HttpExceptionFilter } from '../common/filters/http-exception.filter';

@ApiTags('Tenant')
@Controller('tenant')
@UseFilters(HttpExceptionFilter)
export class TenantController {
    private readonly logger = new Logger(TenantController.name);

    constructor(private readonly tenantService: TenantService) {
    }

    @Post()
    @ApiOperation({summary: 'Create a new tenant'})
    @ApiResponse({
        status: 201,
        description: 'Tenant created successfully.',
        schema: {
            type: 'object',
            properties: {
                data: { $ref: '#/components/schemas/Tenant' },
                message: { type: 'string' },
                success: { type: 'boolean' },
                timestamp: { type: 'string' }
            }
        }
    })
    @ApiResponse({status: 400, description: 'Bad Request - Missing required fields or invalid data.'})
    @ApiResponse({status: 409, description: 'Conflict - Tenant with this name already exists.'})
    @ApiResponse({status: 500, description: 'Internal Server Error.'})
    async create(@Body() createTenantDto: CreateTenantDto) {
        this.logger.log(`Creating tenant: ${createTenantDto.name}`);
        return await this.tenantService.create(createTenantDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all tenants with pagination'})
    @ApiResponse({
        status: 200,
        description: 'Tenants retrieved successfully.',
        schema: {
            type: 'object',
            properties: {
                data: { type: 'array', items: { $ref: '#/components/schemas/Tenant' } },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        perPage: { type: 'number' },
                        totalPages: { type: 'number' }
                    }
                },
                message: { type: 'string' },
                success: { type: 'boolean' },
                timestamp: { type: 'string' }
            }
        }
    })
    @ApiResponse({status: 400, description: 'Bad Request - Invalid query parameters.'})
    @ApiResponse({status: 500, description: 'Internal Server Error.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1, description: 'Page number (starts from 1)'})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10, description: 'Number of items per page'})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt', description: 'Field to sort by'})
    @ApiQuery({
        name: 'order',
        required: false,
        enum: ['ASC', 'DESC'],
        example: 'ASC',
        description: 'Sort order'
    })
    async findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',) {
        this.logger.log(`Fetching tenants - Page: ${page}, PerPage: ${perPage}`);
        return await this.tenantService.findAll(+page, +perPage, sort, order);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve tenant by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Tenant ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0',
    })
    @ApiResponse({
        status: 200,
        description: 'Tenant retrieved successfully.',
        schema: {
            type: 'object',
            properties: {
                data: { $ref: '#/components/schemas/Tenant' },
                message: { type: 'string' },
                success: { type: 'boolean' },
                timestamp: { type: 'string' }
            }
        }
    })
    @ApiResponse({status: 400, description: 'Bad Request - Invalid identifier format.'})
    @ApiResponse({status: 404, description: 'Tenant with specified ID or name not found.'})
    @ApiResponse({status: 500, description: 'Internal Server Error.'})
    async findOne(@Param('identifier') identifier: string) {
        this.logger.log(`Finding tenant: ${identifier}`);
        return await this.tenantService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @ApiOperation({summary: 'Update tenant by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Tenant ID (UUID) or name',
        type: String,
        example: 'ac1c78df-73f0-486f-bc83-a7c9b275199d'
    })
    @ApiResponse({
        status: 200,
        description: 'Tenant updated successfully.',
        schema: {
            type: 'object',
            properties: {
                data: { $ref: '#/components/schemas/Tenant' },
                message: { type: 'string' },
                success: { type: 'boolean' },
                timestamp: { type: 'string' }
            }
        }
    })
    @ApiResponse({status: 400, description: 'Bad Request - Invalid data or identifier.'})
    @ApiResponse({status: 404, description: 'Tenant with specified ID or name not found.'})
    @ApiResponse({status: 409, description: 'Conflict - Tenant with the provided name already exists.'})
    @ApiResponse({status: 500, description: 'Internal Server Error.'})
    async update(
        @Param('identifier') identifier: string,
        @Body() updateTenantDto: UpdateTenantDto
    ) {
        this.logger.log(`Updating tenant: ${identifier}`);
        return await this.tenantService.update(identifier, updateTenantDto);
    }

    @Delete(':id')
    @ApiOperation({summary: 'Delete tenant by ID'})
    @ApiParam({
        name: 'id',
        description: 'Tenant ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'Tenant deleted successfully.',
        schema: {
            type: 'object',
            properties: {
                data: { type: 'null' },
                message: { type: 'string' },
                success: { type: 'boolean' },
                timestamp: { type: 'string' }
            }
        }
    })
    @ApiResponse({status: 400, description: 'Bad Request - Invalid UUID format.'})
    @ApiResponse({status: 404, description: 'Tenant with specified ID not found.'})
    @ApiResponse({status: 500, description: 'Internal Server Error.'})
    async remove(@Param('id', new ParseUUIDPipe()) id: string) {
        this.logger.log(`Deleting tenant: ${id}`);
        return await this.tenantService.remove(id);
    }

}
