<script setup lang="ts">
import {type PropType} from 'vue'
import {type Tenant} from '../types'
import {VaButton, VaCard, VaCardContent, VaDivider, VaInnerLoading} from "vuestic-ui";

defineProps({
    tenants: {
        type: Array as PropType<Tenant[]>,
        required: true,
    },
    loading: {
        type: Boolean,
        required: true,
    },
})

defineEmits<{
    (event: 'edit', tenant: Tenant): void
    (event: 'delete', tenant: Tenant): void
}>()
</script>

<template>
    <VaInnerLoading
        v-if="tenants.length > 0 || loading"
        :loading="loading"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 min-h-[4rem]"
    >
        <VaCard
            v-for="tenant in tenants"
            :key="tenant.id"
            :style="{ '--va-card-outlined-border': '1px solid var(--va-background-element)' }"
            outlined
        >
            <VaCardContent class="flex flex-col h-full">
                <div class="flex justify-between">
                    <div class="text-[var(--va-secondary)]">Created:
                        {{ new Date(tenant.createdAt).toLocaleDateString() }}
                    </div>
                    <div class="text-[var(--va-secondary)]">Updated:
                        {{ new Date(tenant.updatedAt).toLocaleDateString() }}
                    </div>
                </div>
                <div class="flex flex-col items-center gap-4 grow">
                    <h4 class="va-h4 text-center self-stretch overflow-hidden line-clamp-2 text-ellipsis">
                        {{ tenant.name }}
                    </h4>
                    <!--                    <p class="text-[var(&#45;&#45;va-secondary)] text-center">-->
                    <!--                        tenant-->
                    <!--                    </p>-->
                </div>
                <VaDivider class="my-6"/>
                <div class="flex justify-between">
                    <VaButton preset="secondary" icon="mso-edit" color="secondary" @click="$emit('edit', tenant)"/>
                    <VaButton preset="secondary" icon="mso-delete" color="danger"
                              @click="$emit('delete', tenant)"/>
                </div>
            </VaCardContent>
        </VaCard>
    </VaInnerLoading>
    <div v-else class="p-4 flex justify-center items-center text-[var(--va-secondary)]">No tenants</div>
</template>
