<script setup lang="ts">
import {computed, ref, watch} from 'vue'
import {EmptyVehicleType, VehicleType} from '../types'

const props = defineProps<{
    vehicleType: VehicleType | null
    saveButtonLabel: string
}>()

const emit = defineEmits<{
    (event: 'save', vehicleType: VehicleType): void
    (event: 'close'): void
}>()

const defaultNewVehicleType: EmptyVehicleType = {
    name: '',
}

const newVehicleType = ref<VehicleType | EmptyVehicleType>({...defaultNewVehicleType})

const isFormHasUnsavedChanges = computed(() => {
    if (!props.vehicleType) {
        // Creating new - check if name is different from empty
        return newVehicleType.value.name !== defaultNewVehicleType.name
    }
    // Editing existing - check if name is different from original
    return newVehicleType.value.name !== props.vehicleType.name
})

defineExpose({
    isFormHasUnsavedChanges,
})

watch(
    () => props.vehicleType,
    () => {
        if (!props.vehicleType) {
            newVehicleType.value = {...defaultNewVehicleType}
            return
        }

        newVehicleType.value = {
            ...props.vehicleType,
            name: props.vehicleType.name,
        }
    },
    {immediate: true},
)

const required = (v: string) => !!v || 'This field is required'

const handleSave = () => {
    console.log('handleSave called')
    console.log('newVehicleType.value:', newVehicleType.value)
    console.log('props.vehicleType:', props.vehicleType)

    // Create the object to emit
    const vehicleTypeToSave = props.vehicleType
        ? { ...props.vehicleType, name: newVehicleType.value.name } // Editing: preserve all fields, update name
        : { name: newVehicleType.value.name } // Creating: only name

    console.log('Emitting:', vehicleTypeToSave)
    emit('save', vehicleTypeToSave as VehicleType)
}
</script>

<template>
    <VaForm v-slot="{ validate }" class="flex flex-col gap-2">
        <VaInput v-model="newVehicleType.name" label="Vehicle Type Name" :rules="[required]"/>
        <div class="flex justify-end flex-col-reverse sm:flex-row mt-4 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Cancel</VaButton>
            <VaButton @click="validate() && handleSave()">{{ saveButtonLabel }}</VaButton>
        </div>
    </VaForm>
</template>