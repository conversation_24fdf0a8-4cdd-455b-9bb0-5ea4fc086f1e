import { computed, ref, Ref, unref } from 'vue';
import { Pagination, Sorting } from '../../../data/pages/vehicleTypes';
import { VehicleType } from '../types';
import { useVehicleTypesStore } from '../../../stores/vehicleTypeStores';

const makePaginationRef = () => ref<Pagination>({ page: 1, perPage: 10, total: 0 });
const makeSortingRef = () => ref<Sorting>({ sortBy: 'createdAt', sortingOrder: 'DESC' });

export const useVehicleTypes = (options?: { sorting?: Ref<Sorting>; pagination?: Ref<Pagination> }) => {
    const isLoading = ref(false);
    const vehicleTypesStore = useVehicleTypesStore();

    const { sorting = makeSortingRef(), pagination = makePaginationRef() } = options ?? {};

    const fetch = async () => {
        isLoading.value = true;
        await vehicleTypesStore.getAll({
            sorting: unref(sorting),
            pagination: unref(pagination),
        });
        pagination.value = vehicleTypesStore.pagination;
        isLoading.value = false;
    };

    const vehicleTypes = computed(() => {
        const paginated = vehicleTypesStore.items.slice(
            (pagination.value.page - 1) * pagination.value.perPage,
            pagination.value.page * pagination.value.perPage,
        );

        const getSortItem = (obj: any, sortBy: Sorting['sortBy']) => {
            if (sortBy === 'name') {
                return obj.name;
            }

            if (sortBy === 'updatedAt') {
                 return new Date(obj[sortBy]);
            }

            if (sortBy === 'createdAt') {
                return new Date(obj[sortBy]);
            }

            return obj[sortBy];
        };

        if (sorting.value.sortBy && sorting.value.sortingOrder) {
            paginated.sort((a, b) => {
                a = getSortItem(a, sorting.value.sortBy!);
                b = getSortItem(b, sorting.value.sortBy!);

                if (a < b) {
                    return sorting.value.sortingOrder === 'asc' ? -1 : 1;
                }
                if (a > b) {
                    return sorting.value.sortingOrder === 'asc' ? 1 : -1;
                }
                return 0;
            });
        }

        return paginated;
    });

    fetch();

    return {
        isLoading,
        vehicleTypes,
        fetch,
        async add(vehicleType: Omit<VehicleType, 'id' | 'createdAt'>) {
            isLoading.value = true;
            await vehicleTypesStore.add(vehicleType);
            await fetch();
            isLoading.value = false;
        },

        async update(vehicleType: VehicleType) {
            isLoading.value = true;
            console.log("Vehicle Type:...........",vehicleType)
            await vehicleTypesStore.update(vehicleType);
            await fetch();
            isLoading.value = false;
        },

        async remove(vehicleType: VehicleType) {
            isLoading.value = true;
            await vehicleTypesStore.remove(vehicleType);
            await fetch();
            isLoading.value = false;
        },
        pagination,
        sorting,
    };
};
