import {
    BadRequestException,
    ConflictException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    Logger
} from '@nestjs/common';
import {CreateClientDto} from './dto/create-client.dto';
import {ClientResponseDto} from "./dto/client-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {Client} from "./entities/client.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateClientDto} from "./dto/update-client.dto";
import { ResponseUtil } from '../common/utils/response.util';
import { ApiResponse, PaginatedApiResponse } from '../common/interfaces/api-response.interface';

@Injectable()
export class ClientService {
    private readonly logger = new Logger(ClientService.name);

    constructor(
        @InjectRepository(Client) private readonly clientRepository: Repository<Client>,
    ) {
    }

    async create(createClientDto: CreateClientDto): Promise<ApiResponse<ClientResponseDto>> {
        this.logger.log(`Creating new client: ${createClientDto.fullName}`);
        const {fullName, email, phone} = createClientDto;

        try {
            // Validate required fields
            this.validateRequiredFields(createClientDto);

            // Check for existing client
            await this.checkExistingClient(fullName, email, phone);

            const client = this.clientRepository.create({...createClientDto});

            let savedClient;
            try {
                savedClient = await this.clientRepository.save(client);
            } catch (error) {
                // NOT NULL violation
                if (error.code === '23502') {
                    const columnName = error.column || 'a required field';
                    throw new BadRequestException(`Missing required field: ${columnName}`);
                }

                // Unique violation
                if (error.code === '23505') {
                    throw new ConflictException('A client with this information already exists');
                }

                this.logger.error('Unexpected error while saving client:', error);
                throw new InternalServerErrorException('Unexpected error while creating client.');
            }

            const responseData = plainToInstance(ClientResponseDto, savedClient, {
                excludeExtraneousValues: true,
            });

            this.logger.log(`Successfully created client: ${savedClient.fullName}`);

            return ResponseUtil.success(
                responseData,
                'Client created successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to create client: ${error.message}`, error.stack);
            throw error;
        }
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<PaginatedApiResponse<ClientResponseDto[]>> {
        this.logger.log(`Fetching clients - Page: ${page}, PerPage: ${perPage}, Sort: ${sort}, Order: ${order}`);
        try {
            const [records, total] = await this.clientRepository.findAndCount({
                order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
                skip: (page - 1) * perPage,
                take: perPage,
            });

            const data = plainToInstance(ClientResponseDto, records, {
                excludeExtraneousValues: true,
            });

            const meta = {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            };

            this.logger.log(`Successfully retrieved ${records.length} clients`);

            return ResponseUtil.paginated(
                data,
                meta,
                'Clients retrieved successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to retrieve clients: ${error.message}`, error.stack);
            throw new InternalServerErrorException('Failed to retrieve clients');
        }
    }

    async findOneByIdOrName(query: string): Promise<ApiResponse<ClientResponseDto>> {
        this.logger.log(`Finding client by ID or name: ${query}`);
        try {
            if (!query) {
                throw new BadRequestException('Query parameter is required');
            }

            let client: Client | null = null;

            if (isUUID(query)) {
                client = await this.clientRepository.findOne({
                    where: {id: query},
                });
            }

            if (!client) {
                client = await this.clientRepository.findOne({
                    where: {fullName: query},
                });
            }

            if (!client) {
                throw new NotFoundException(
                    `Client with ID or name "${query}" not found`,
                );
            }

            const data = plainToInstance(ClientResponseDto, client, {
                excludeExtraneousValues: true,
            });

            this.logger.log(`Successfully found client: ${client.fullName}`);

            return ResponseUtil.success(
                data,
                'Client retrieved successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to find client: ${error.message}`, error.stack);
            throw error;
        }
    }

    async update(
        identifier: string,
        updateClientDto: UpdateClientDto,
    ): Promise<ApiResponse<ClientResponseDto>> {
        this.logger.log(`Updating client: ${identifier}`);
        try {
            if (!identifier) {
                throw new BadRequestException('Identifier is required');
            }

            const isUuid = isUUID(identifier);

            const client = await this.clientRepository.findOne({
                where: isUuid ? {id: identifier} : {fullName: identifier},
            });

            if (!client) {
                throw new NotFoundException('Client with ID or name not found.');
            }

            // Check for conflicts if updating unique fields
            if (updateClientDto.fullName && updateClientDto.fullName !== client.fullName) {
                const existing = await this.clientRepository.findOne({
                    where: {fullName: updateClientDto.fullName},
                });

                if (existing) {
                    throw new ConflictException('Client with the provided name already exists.');
                }
            }

            if (updateClientDto.email && updateClientDto.email !== client.email) {
                const existing = await this.clientRepository.findOne({
                    where: {email: updateClientDto.email},
                });

                if (existing) {
                    throw new ConflictException('Client with the provided email already exists.');
                }
            }

            if (updateClientDto.phone && updateClientDto.phone !== client.phone) {
                const existing = await this.clientRepository.findOne({
                    where: {phone: updateClientDto.phone},
                });

                if (existing) {
                    throw new ConflictException('Client with the provided phone number already exists.');
                }
            }

            Object.assign(client, updateClientDto);

            let updated;
            try {
                updated = await this.clientRepository.save(client);
            } catch (error) {
                if (error.code === '23505') {
                    throw new ConflictException('A client with this information already exists');
                }
                this.logger.error('Unexpected error while updating client:', error);
                throw new InternalServerErrorException('Unexpected error while updating client.');
            }

            const data = plainToInstance(ClientResponseDto, updated, {
                excludeExtraneousValues: true,
            });

            this.logger.log(`Successfully updated client: ${updated.fullName}`);

            return ResponseUtil.success(
                data,
                'Client updated successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to update client: ${error.message}`, error.stack);
            throw error;
        }
    }

    async remove(id: string): Promise<ApiResponse<null>> {
        this.logger.log(`Removing client: ${id}`);
        try {
            if (!id) {
                throw new BadRequestException('ID is required');
            }

            if (!isUUID(id)) {
                throw new BadRequestException('Invalid ID format');
            }

            const client = await this.clientRepository.findOne({where: {id}});

            if (!client) {
                throw new NotFoundException(`Client with id ${id} not found`);
            }

            await this.clientRepository.remove(client);

            this.logger.log(`Successfully removed client: ${client.fullName}`);

            return ResponseUtil.success(
                null,
                'Client deleted successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to remove client: ${error.message}`, error.stack);
            throw error;
        }
    }

    // Helper methods
    private validateRequiredFields(createClientDto: CreateClientDto): void {
        const requiredFields: Record<string, string> = {
            fullName: 'Full name',
            email: 'Email',
            phone: 'Phone number',
            address: 'Address',
            city: 'City',
            country: 'Country',
            postalCode: 'Postal code',
        };

        for (const [field, label] of Object.entries(requiredFields)) {
            if (!createClientDto[field]) {
                throw new BadRequestException(`Missing required field: ${label}`);
            }
        }
    }

    private async checkExistingClient(fullName: string, email: string, phone: string): Promise<void> {
        const existingClient = await this.clientRepository.findOne({
            where: [{fullName}, {email}, {phone}],
        });

        if (existingClient) {
            if (existingClient.fullName === fullName) {
                throw new ConflictException('A client with this name already exists');
            }

            if (existingClient.email === email) {
                throw new ConflictException('A client with this email already exists');
            }

            if (existingClient.phone === phone) {
                throw new ConflictException('A client with this phone number already exists');
            }
        }
    }
}
